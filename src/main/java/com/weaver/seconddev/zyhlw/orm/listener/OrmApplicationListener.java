package com.weaver.seconddev.zyhlw.orm.listener;

import com.weaver.seconddev.zyhlw.orm.cache.OrmCacheManager;
import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ORM应用监听器，监听应用启动事件
 * 在应用启动完成后执行ORM相关的初始化操作
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class OrmApplicationListener implements ApplicationListener<ContextRefreshedEvent> {

    private static final Logger logger = LoggerFactory.getLogger(OrmApplicationListener.class);

    @Resource
    private OrmCacheManager ormCacheManager;

    /**
     * 应用启动完成后的处理逻辑
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 只在根容器启动时执行，避免重复执行
        if (event.getApplicationContext().getParent() == null) {
            try {
                logger.info("{} ORM系统启动完成，开始执行初始化操作", OrmConstants.Log.CACHE_OPERATION_PREFIX);
                
                // 打印ORM系统信息
                printOrmSystemInfo();
                
                // 打印初始缓存状态
                printInitialCacheStatus();
                
                logger.info("{} ORM系统初始化操作完成", OrmConstants.Log.CACHE_OPERATION_PREFIX);
                
            } catch (Exception e) {
                logger.error("{} ORM系统初始化操作失败", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            }
        }
    }

    /**
     * 打印ORM系统信息
     */
    private void printOrmSystemInfo() {
        logger.info("{} ========== ORM系统信息 ==========", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{} 版本: 1.0.0", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{} 功能模块:", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{}   - 实体元数据管理", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{}   - Repository工厂", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{}   - 动态代理", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{}   - 缓存管理", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{}   - 查询构建器", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{} 管理接口: /api/orm/management/*", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        logger.info("{} ================================", OrmConstants.Log.CACHE_OPERATION_PREFIX);
    }

    /**
     * 打印初始缓存状态
     */
    private void printInitialCacheStatus() {
        try {
            logger.info("{} ========== 初始缓存状态 ==========", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
            OrmCacheManager.CacheHealthInfo healthInfo = ormCacheManager.getCacheHealthInfo();
            logger.info("{} 缓存健康状态: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, 
                    healthInfo.isHealthy() ? "正常" : "异常");
            logger.info("{} Repository缓存大小: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, 
                    healthInfo.getRepositoryCacheSize());
            logger.info("{} Repository缓存命中率: {:.2f}%", OrmConstants.Log.CACHE_OPERATION_PREFIX, 
                    healthInfo.getRepositoryCacheHitRate() * 100);
            logger.info("{} 代理缓存大小: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, 
                    healthInfo.getProxyCacheSize());
            
            logger.info("{} ================================", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
        } catch (Exception e) {
            logger.warn("{} 获取初始缓存状态失败: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, e.getMessage());
        }
    }
}
