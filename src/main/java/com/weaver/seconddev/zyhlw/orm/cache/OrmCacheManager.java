package com.weaver.seconddev.zyhlw.orm.cache;

import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.factory.RepositoryFactory;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import com.weaver.seconddev.zyhlw.orm.repository.RepositoryProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ORM缓存管理器，提供统一的缓存管理功能
 * 包括清空所有ORM相关缓存、获取缓存统计信息等
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class OrmCacheManager {

    private static final Logger logger = LoggerFactory.getLogger(OrmCacheManager.class);

    @Resource
    private EntityMetadataManager entityMetadataManager;

    @Resource
    private RepositoryFactory repositoryFactory;

    /**
     * 清空所有ORM相关缓存
     * 包括：实体元数据缓存、Repository实例缓存、代理缓存
     */
    public void clearAllCache() {
        logger.info("{} 开始清空所有ORM缓存", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        
        try {
            // 清空实体元数据缓存
            entityMetadataManager.clearCache();
            
            // 清空Repository实例缓存
            repositoryFactory.clearCache();
            
            // 清空代理缓存
            RepositoryProxy.clearProxyCache();
            
            logger.info("{} 成功清空所有ORM缓存", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
        } catch (Exception e) {
            logger.error("{} 清空ORM缓存时发生异常", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            throw new RuntimeException("清空ORM缓存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清空指定实体类的相关缓存
     *
     * @param entityClass 实体类
     */
    public void clearEntityCache(Class<?> entityClass) {
        if (entityClass == null) {
            logger.warn("{} 实体类为null，跳过清空缓存", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            return;
        }

        logger.info("{} 开始清空实体类缓存: {}", 
                OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());
        
        try {
            // 移除Repository实例缓存
            repositoryFactory.removeFromCache(entityClass);
            
            logger.info("{} 成功清空实体类缓存: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());
            
        } catch (Exception e) {
            logger.error("{} 清空实体类缓存时发生异常: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName(), e);
            throw new RuntimeException("清空实体类缓存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有ORM缓存的统计信息
     *
     * @return 缓存统计信息
     */
    public String getAllCacheStats() {
        StringBuilder stats = new StringBuilder();
        
        try {
            // 实体元数据缓存统计
            stats.append("=== ORM缓存统计信息 ===\n");
            stats.append("1. 实体元数据缓存:\n");
            stats.append("   ").append(entityMetadataManager.getCacheStats()).append("\n");
            
            // Repository实例缓存统计
            stats.append("2. Repository实例缓存:\n");
            stats.append("   ").append(repositoryFactory.getCacheStats()).append("\n");
            
            // 代理缓存统计
            stats.append("3. Repository代理缓存:\n");
            stats.append("   缓存大小: ").append(RepositoryProxy.getProxyCacheSize()).append("\n");
            
            stats.append("=== 统计信息结束 ===");
            
        } catch (Exception e) {
            logger.error("{} 获取缓存统计信息时发生异常", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            stats.append("获取缓存统计信息失败: ").append(e.getMessage());
        }
        
        return stats.toString();
    }

    /**
     * 打印所有ORM缓存的统计信息
     */
    public void printAllCacheStats() {
        String stats = getAllCacheStats();
        logger.info("{} \n{}", OrmConstants.Log.CACHE_OPERATION_PREFIX, stats);
    }

    /**
     * 获取缓存健康状态
     *
     * @return 缓存健康状态信息
     */
    public CacheHealthInfo getCacheHealthInfo() {
        try {
            CacheHealthInfo healthInfo = new CacheHealthInfo();
            
            // Repository缓存信息
            healthInfo.setRepositoryCacheSize(repositoryFactory.getCacheSize());
            healthInfo.setRepositoryCacheHitRate(repositoryFactory.getCacheHitRate());
            
            // 代理缓存信息
            healthInfo.setProxyCacheSize(RepositoryProxy.getProxyCacheSize());
            
            // 整体健康状态
            healthInfo.setHealthy(true);
            healthInfo.setMessage("所有缓存运行正常");
            
            return healthInfo;
            
        } catch (Exception e) {
            logger.error("{} 获取缓存健康状态时发生异常", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            
            CacheHealthInfo healthInfo = new CacheHealthInfo();
            healthInfo.setHealthy(false);
            healthInfo.setMessage("获取缓存健康状态失败: " + e.getMessage());
            return healthInfo;
        }
    }

    /**
     * 缓存健康状态信息
     */
    public static class CacheHealthInfo {
        private boolean healthy;
        private String message;
        private int repositoryCacheSize;
        private double repositoryCacheHitRate;
        private int proxyCacheSize;

        // Getters and Setters
        public boolean isHealthy() {
            return healthy;
        }

        public void setHealthy(boolean healthy) {
            this.healthy = healthy;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getRepositoryCacheSize() {
            return repositoryCacheSize;
        }

        public void setRepositoryCacheSize(int repositoryCacheSize) {
            this.repositoryCacheSize = repositoryCacheSize;
        }

        public double getRepositoryCacheHitRate() {
            return repositoryCacheHitRate;
        }

        public void setRepositoryCacheHitRate(double repositoryCacheHitRate) {
            this.repositoryCacheHitRate = repositoryCacheHitRate;
        }

        public int getProxyCacheSize() {
            return proxyCacheSize;
        }

        public void setProxyCacheSize(int proxyCacheSize) {
            this.proxyCacheSize = proxyCacheSize;
        }

        @Override
        public String toString() {
            return String.format("CacheHealthInfo{healthy=%s, message='%s', repositoryCacheSize=%d, repositoryCacheHitRate=%.2f%%, proxyCacheSize=%d}",
                    healthy, message, repositoryCacheSize, repositoryCacheHitRate * 100, proxyCacheSize);
        }
    }
}
