package com.weaver.seconddev.zyhlw.orm.metadata;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 表元数据实体类，存储表和字段映射信息
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@SuppressWarnings("ALL")
public class TableMetadata implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实体类
     */
    private Class<?> entityClass;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 主键字段名
     */
    private String primaryKeyField;

    /**
     * 主键数据库字段名
     */
    private String primaryKeyColumn;

    /**
     * 字段映射：实体字段名 -> 数据库字段名
     */
    private final Map<String, String> fieldToColumnMap;

    /**
     * 字段映射：数据库字段名 -> 实体字段名
     */
    private final Map<String, String> columnToFieldMap;

    /**
     * 字段类型映射：实体字段名 -> 字段类型
     */
    private final Map<String, Class<?>> fieldTypeMap;

    /**
     * 字段对象映射：实体字段名 -> Field对象
     */
    private final Map<String, Field> fieldMap;

    /**
     * 是否包含逻辑删除字段
     */
    private boolean hasLogicDelete;

    /**
     * 逻辑删除字段名
     */
    private String logicDeleteField;

    /**
     * 逻辑删除字段的数据库字段名
     */
    private String logicDeleteColumn;

    /**
     * 是否包含租户字段
     */
    private boolean hasTenant;

    /**
     * 租户字段名
     */
    private String tenantField;

    /**
     * 租户字段的数据库字段名
     */
    private String tenantColumn;

    public TableMetadata() {
        this.fieldToColumnMap = new ConcurrentHashMap<>();
        this.columnToFieldMap = new ConcurrentHashMap<>();
        this.fieldTypeMap = new ConcurrentHashMap<>();
        this.fieldMap = new ConcurrentHashMap<>();
    }

    public TableMetadata(Class<?> entityClass, String tableName) {
        this();
        this.entityClass = entityClass;
        this.tableName = tableName;
    }

    /**
     * 添加字段映射
     *
     * @param fieldName  实体字段名
     * @param columnName 数据库字段名
     * @param fieldType  字段类型
     * @param field      字段对象
     */
    public void addFieldMapping(String fieldName, String columnName, Class<?> fieldType, Field field) {
        fieldToColumnMap.put(fieldName, columnName);
        columnToFieldMap.put(columnName, fieldName);
        fieldTypeMap.put(fieldName, fieldType);
        fieldMap.put(fieldName, field);
    }

    /**
     * 根据实体字段名获取数据库字段名
     *
     * @param fieldName 实体字段名
     * @return 数据库字段名
     */
    public String getColumnName(String fieldName) {
        return fieldToColumnMap.get(fieldName);
    }

    /**
     * 根据数据库字段名获取实体字段名
     *
     * @param columnName 数据库字段名
     * @return 实体字段名
     */
    public String getFieldName(String columnName) {
        return columnToFieldMap.get(columnName);
    }

    /**
     * 根据实体字段名获取字段类型
     *
     * @param fieldName 实体字段名
     * @return 字段类型
     */
    public Class<?> getFieldType(String fieldName) {
        return fieldTypeMap.get(fieldName);
    }

    /**
     * 根据实体字段名获取Field对象
     *
     * @param fieldName 实体字段名
     * @return Field对象
     */
    public Field getField(String fieldName) {
        return fieldMap.get(fieldName);
    }

    /**
     * 检查是否包含指定字段
     *
     * @param fieldName 实体字段名
     * @return 是否包含
     */
    public boolean hasField(String fieldName) {
        return fieldToColumnMap.containsKey(fieldName);
    }

    /**
     * 检查是否包含指定数据库字段
     *
     * @param columnName 数据库字段名
     * @return 是否包含
     */
    public boolean hasColumn(String columnName) {
        return columnToFieldMap.containsKey(columnName);
    }

    // Getters and Setters
    public Class<?> getEntityClass() {
        return entityClass;
    }

    public void setEntityClass(Class<?> entityClass) {
        this.entityClass = entityClass;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getPrimaryKeyField() {
        return primaryKeyField;
    }

    public void setPrimaryKeyField(String primaryKeyField) {
        this.primaryKeyField = primaryKeyField;
    }

    public String getPrimaryKeyColumn() {
        return primaryKeyColumn;
    }

    public void setPrimaryKeyColumn(String primaryKeyColumn) {
        this.primaryKeyColumn = primaryKeyColumn;
    }

    public Map<String, String> getFieldToColumnMap() {
        return fieldToColumnMap;
    }

    public Map<String, String> getColumnToFieldMap() {
        return columnToFieldMap;
    }

    public Map<String, Class<?>> getFieldTypeMap() {
        return fieldTypeMap;
    }

    public Map<String, Field> getFieldMap() {
        return fieldMap;
    }

    public boolean isHasLogicDelete() {
        return hasLogicDelete;
    }

    public void setHasLogicDelete(boolean hasLogicDelete) {
        this.hasLogicDelete = hasLogicDelete;
    }

    public String getLogicDeleteField() {
        return logicDeleteField;
    }

    public void setLogicDeleteField(String logicDeleteField) {
        this.logicDeleteField = logicDeleteField;
    }

    public String getLogicDeleteColumn() {
        return logicDeleteColumn;
    }

    public void setLogicDeleteColumn(String logicDeleteColumn) {
        this.logicDeleteColumn = logicDeleteColumn;
    }

    public boolean isHasTenant() {
        return hasTenant;
    }

    public void setHasTenant(boolean hasTenant) {
        this.hasTenant = hasTenant;
    }

    public String getTenantField() {
        return tenantField;
    }

    public void setTenantField(String tenantField) {
        this.tenantField = tenantField;
    }

    public String getTenantColumn() {
        return tenantColumn;
    }

    public void setTenantColumn(String tenantColumn) {
        this.tenantColumn = tenantColumn;
    }

    @Override
    public String toString() {
        return "TableMetadata{" +
                "entityClass=" + entityClass +
                ", tableName='" + tableName + '\'' +
                ", primaryKeyField='" + primaryKeyField + '\'' +
                ", primaryKeyColumn='" + primaryKeyColumn + '\'' +
                ", hasLogicDelete=" + hasLogicDelete +
                ", hasTenant=" + hasTenant +
                '}';
    }
}
