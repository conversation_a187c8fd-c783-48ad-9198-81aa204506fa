package com.weaver.seconddev.zyhlw.ebuilder.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * 经费预算
 *
 * <AUTHOR>
 * @date 2025年06月27日 10:56
 */
@TableName("uf_jing_fysgl")
@Getter
@Setter
public class OutlayBudgetDO extends BaseDO {

    /**
     * 预算单号
     */
    @TableField("yu_sd")
    private String budgetNo;

    /**
     * 预算流程
     */
    @TableField("yu_slc")
    private Long budgetWorkflowId;

    /**
     * 申请人
     */
    @TableField("shen_qr")
    private Long applicant;

    /**
     * 申请日期
     */
    @TableField("shen_qrq")
    private String applicationDate;

    /**
     * 经费类型
     */
    @TableField("jing_flx")
    private String expenseType;

    /**
     * 兴趣小组
     */
    @TableField("xu_qxz")
    private Long interestGroup;

    /**
     * 工会小组
     */
    @TableField("gong_hxz")
    private Long unionGroup;

    /**
     * 预算总金额
     */
    @TableField("yu_szje")
    private Float totalBudgetAmount;

    /**
     * 预算剩余金额
     */
    @TableField("yu_ssyje")
    private Float remainingBudgetAmount;

    /**
     * 申请部门
     */
    @TableField("shen_qbm")
    private Long applicationDepartment;

    /**
     * 兴趣小组/工会小组
     */
    @TableField("xing_qxzghxz")
    private String interestOrUnionGroup;

    /**
     * 兴趣小组文本
     */
    @TableField("xing_qxzwb")
    private String interestGroupText;

    /**
     * 工会小组文本
     */
    @TableField("gong_hxzwb")
    private String unionGroupText;
}
