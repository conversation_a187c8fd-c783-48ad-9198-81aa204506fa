package com.weaver.seconddev.zyhlw.orm.factory;

import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepository;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepositoryImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Repository工厂类，提供Repository实例的创建和管理
 * 参考现有Factory模式，支持泛型类型推断和缓存机制
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class RepositoryFactory {

    private static final Logger logger = LoggerFactory.getLogger(RepositoryFactory.class);

    @Resource
    private ApplicationContext applicationContext;

    /**
     * Repository实例缓存：实体类 -> Repository实例
     */
    private static final ConcurrentHashMap<Class<?>, BaseRepository<?>> REPOSITORY_CACHE = new ConcurrentHashMap<>();

    /**
     * 缓存统计信息
     */
    private static final AtomicLong cacheHitCount = new AtomicLong(0);
    private static final AtomicLong cacheMissCount = new AtomicLong(0);

    /**
     * 获取指定实体类的Repository实例
     *
     * @param entityClass 实体类
     * @param <T>         实体类型
     * @return Repository实例
     */
    @SuppressWarnings("unchecked")
    public <T> BaseRepository<T> getRepository(Class<T> entityClass) {
        if (entityClass == null) {
            throw new OrmException.ConfigurationException("实体类不能为null");
        }

        try {
            logger.debug("{} 获取Repository实例，实体类: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());

            // 先从缓存中获取
            BaseRepository<T> repository = (BaseRepository<T>) REPOSITORY_CACHE.get(entityClass);
            if (repository != null) {
                cacheHitCount.incrementAndGet();
                logger.debug("{} 缓存命中，实体类: {}",
                        OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());
                return repository;
            }

            cacheMissCount.incrementAndGet();
            logger.debug("{} 缓存未命中，创建新实例，实体类: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());

            // 创建新的Repository实例
            repository = createRepository(entityClass);

            // 缓存实例
            REPOSITORY_CACHE.put(entityClass, repository);

            logger.debug("{} Repository实例创建完成，实体类: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());

            return repository;

        } catch (Exception e) {
            logger.error("{} 获取Repository实例失败，实体类: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName(), e);
            throw new OrmException.ConfigurationException("获取Repository实例失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过泛型匿名内部类获取Repository实例
     * 用于保留泛型类型信息
     *
     * @param typeReference 泛型类型引用
     * @param <T>           实体类型
     * @return Repository实例
     */
    @SuppressWarnings("unchecked")
    public <T> BaseRepository<T> getRepository(TypeReference<T> typeReference) {
        if (typeReference == null) {
            throw new OrmException.ConfigurationException("类型引用不能为null");
        }

        try {
            Type type = typeReference.getType();
            if (type instanceof Class) {
                return getRepository((Class<T>) type);
            } else if (type instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) type;
                Type rawType = parameterizedType.getRawType();
                if (rawType instanceof Class) {
                    return getRepository((Class<T>) rawType);
                }
            }

            throw new OrmException.ConfigurationException("无法解析类型引用: " + type);

        } catch (Exception e) {
            logger.error("{} 通过类型引用获取Repository实例失败", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            throw new OrmException.ConfigurationException("通过类型引用获取Repository实例失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建Repository实例
     *
     * @param entityClass 实体类
     * @param <T>         实体类型
     * @return Repository实例
     */
    private <T> BaseRepository<T> createRepository(Class<T> entityClass) {
        try {
            // 从Spring容器中获取BaseRepositoryImpl实例
            BaseRepositoryImpl<T> repositoryImpl = applicationContext.getBean(BaseRepositoryImpl.class);
            
            // 设置实体类型
            repositoryImpl.setEntityClass(entityClass);
            
            return repositoryImpl;

        } catch (Exception e) {
            logger.error("{} 创建Repository实例失败，实体类: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName(), e);
            throw new OrmException.ConfigurationException("创建Repository实例失败: " + e.getMessage(), e);
        }
    }

    /**
     * 移除指定实体类的Repository缓存
     *
     * @param entityClass 实体类
     */
    public void removeFromCache(Class<?> entityClass) {
        if (entityClass != null) {
            REPOSITORY_CACHE.remove(entityClass);
            logger.debug("{} 移除Repository缓存，实体类: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());
        }
    }

    /**
     * 清空所有Repository缓存
     */
    public void clearCache() {
        REPOSITORY_CACHE.clear();
        cacheHitCount.set(0);
        cacheMissCount.set(0);
        logger.info("{} 清空所有Repository缓存", OrmConstants.Log.CACHE_OPERATION_PREFIX);
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public int getCacheSize() {
        return REPOSITORY_CACHE.size();
    }

    /**
     * 获取缓存命中率
     *
     * @return 缓存命中率（0.0 - 1.0）
     */
    public double getCacheHitRate() {
        long total = cacheHitCount.get() + cacheMissCount.get();
        return total == 0 ? 0.0 : (double) cacheHitCount.get() / total;
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        long total = cacheHitCount.get() + cacheMissCount.get();
        return String.format("Repository缓存统计 - 总访问: %d, 命中: %d, 未命中: %d, 命中率: %.2f%%, 缓存大小: %d",
                total, cacheHitCount.get(), cacheMissCount.get(), getCacheHitRate() * 100, getCacheSize());
    }

    /**
     * 打印缓存统计信息
     */
    public void printCacheStats() {
        logger.info("{} {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, getCacheStats());
    }

    /**
     * 检查Repository是否已缓存
     *
     * @param entityClass 实体类
     * @return 是否已缓存
     */
    public boolean isCached(Class<?> entityClass) {
        return REPOSITORY_CACHE.containsKey(entityClass);
    }

    /**
     * 泛型类型引用抽象类
     * 用于保留泛型类型信息
     *
     * @param <T> 泛型类型
     */
    public static abstract class TypeReference<T> {
        private final Type type;

        protected TypeReference() {
            Type superClass = getClass().getGenericSuperclass();
            if (superClass instanceof ParameterizedType) {
                this.type = ((ParameterizedType) superClass).getActualTypeArguments()[0];
            } else {
                throw new OrmException.ConfigurationException("TypeReference必须使用泛型参数");
            }
        }

        public Type getType() {
            return type;
        }
    }
}
