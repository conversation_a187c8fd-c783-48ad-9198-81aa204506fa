package com.weaver.seconddev.zyhlw.orm.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025年06月27日 11:00
 */
@Data
@Builder(builderMethodName = "baseDOBuilder")
@NoArgsConstructor
@AllArgsConstructor
public class BaseDO implements Serializable {
    /**
     * 数据 ID，与 formdata#id、ebdf_physical_data#form_data_id、ebdf_physical_data#form_table_id 相同，用于维护分布式 ID 值。
     */
    @TableId(type = IdType.NONE)
    private Long id;

    /**
     * 是否为流程数据，0 表示不是，1 表示是。该字段仅在主表中存在，非流程数据也需维护为 0，否则会导致列表中该数据不可见。
     */
    private Boolean isFlow;

    /**
     * 数据状态，0 表示草稿（只有创建者可见），1 表示正式数据。该字段仅在主表中存在。
     */
    private Integer dataStatus;

    /**
     * 数据创建人
     */
    private Long creator;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新人（可为空）。
     */
    private Long updater;

    /**
     * 数据更新时间（可为空）。
     */
    private LocalDateTime updateTime;

    /**
     * 数据删除状态，0 表示正式数据，1 表示回收站数据，2 表示表单彻底删除，3 表示物理待删除 (如需物理删除请看此文档最下方)。eb 标准操作的数据会同步更新，is_delete 废弃字段兼容老逻辑，但建议使用 delete_type。
     */
    private Integer deleteType;

    /**
     * 表单状态，0 表示正常，1 表示表单已删除。该字段仅在主表中存在。
     */
    private Integer ftStatus;

    /**
     * 主表数据与 id 一致，明细数据关联对应主表数据的 id。
     */
    private Long formDataId;

    /**
     * 租户key
     */
    private String tenantKey;
}
