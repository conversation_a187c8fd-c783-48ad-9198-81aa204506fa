package com.weaver.seconddev.zyhlw.controller;

import com.weaver.seconddev.zyhlw.orm.cache.OrmCacheManager;
import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * ORM管理控制器，提供ORM系统的管理功能
 * 包括缓存管理、健康检查、统计信息等
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@RestController
@RequestMapping("/api/orm/management")
public class OrmManagementController {

    private static final Logger logger = LoggerFactory.getLogger(OrmManagementController.class);

    @Resource
    private OrmCacheManager ormCacheManager;

    /**
     * 清空所有ORM缓存
     *
     * @return 操作结果
     */
    @PostMapping("/cache/clear-all")
    public Map<String, Object> clearAllCache() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("{} 接收到清空所有ORM缓存的请求", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
            ormCacheManager.clearAllCache();
            
            result.put("success", true);
            result.put("message", "成功清空所有ORM缓存");
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("{} 成功处理清空所有ORM缓存的请求", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
        } catch (Exception e) {
            logger.error("{} 清空所有ORM缓存失败", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            
            result.put("success", false);
            result.put("message", "清空ORM缓存失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    /**
     * 清空指定实体类的缓存
     *
     * @param entityClassName 实体类名
     * @return 操作结果
     */
    @PostMapping("/cache/clear-entity")
    public Map<String, Object> clearEntityCache(@RequestParam String entityClassName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("{} 接收到清空实体类缓存的请求: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClassName);
            
            // 通过类名获取Class对象
            Class<?> entityClass = Class.forName(entityClassName);
            ormCacheManager.clearEntityCache(entityClass);
            
            result.put("success", true);
            result.put("message", "成功清空实体类缓存: " + entityClassName);
            result.put("entityClass", entityClassName);
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("{} 成功处理清空实体类缓存的请求: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClassName);
            
        } catch (ClassNotFoundException e) {
            logger.error("{} 找不到指定的实体类: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClassName, e);
            
            result.put("success", false);
            result.put("message", "找不到指定的实体类: " + entityClassName);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            logger.error("{} 清空实体类缓存失败: {}", 
                    OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClassName, e);
            
            result.put("success", false);
            result.put("message", "清空实体类缓存失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    /**
     * 获取ORM缓存统计信息
     *
     * @return 缓存统计信息
     */
    @GetMapping("/cache/stats")
    public Map<String, Object> getCacheStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.debug("{} 接收到获取缓存统计信息的请求", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
            String stats = ormCacheManager.getAllCacheStats();
            OrmCacheManager.CacheHealthInfo healthInfo = ormCacheManager.getCacheHealthInfo();
            
            result.put("success", true);
            result.put("stats", stats);
            result.put("healthInfo", healthInfo);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            logger.error("{} 获取缓存统计信息失败", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            
            result.put("success", false);
            result.put("message", "获取缓存统计信息失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    /**
     * 获取ORM缓存健康状态
     *
     * @return 缓存健康状态
     */
    @GetMapping("/cache/health")
    public Map<String, Object> getCacheHealth() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.debug("{} 接收到获取缓存健康状态的请求", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
            OrmCacheManager.CacheHealthInfo healthInfo = ormCacheManager.getCacheHealthInfo();
            
            result.put("success", true);
            result.put("healthy", healthInfo.isHealthy());
            result.put("message", healthInfo.getMessage());
            result.put("repositoryCacheSize", healthInfo.getRepositoryCacheSize());
            result.put("repositoryCacheHitRate", healthInfo.getRepositoryCacheHitRate());
            result.put("proxyCacheSize", healthInfo.getProxyCacheSize());
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            logger.error("{} 获取缓存健康状态失败", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            
            result.put("success", false);
            result.put("healthy", false);
            result.put("message", "获取缓存健康状态失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    /**
     * 打印缓存统计信息到日志
     *
     * @return 操作结果
     */
    @PostMapping("/cache/print-stats")
    public Map<String, Object> printCacheStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("{} 接收到打印缓存统计信息的请求", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            
            ormCacheManager.printAllCacheStats();
            
            result.put("success", true);
            result.put("message", "缓存统计信息已打印到日志");
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            logger.error("{} 打印缓存统计信息失败", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            
            result.put("success", false);
            result.put("message", "打印缓存统计信息失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    /**
     * 获取ORM系统信息
     *
     * @return 系统信息
     */
    @GetMapping("/info")
    public Map<String, Object> getOrmInfo() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("version", "1.0.0");
            result.put("description", "ORM系统管理接口");
            result.put("features", new String[]{
                "实体元数据管理",
                "Repository工厂",
                "动态代理",
                "缓存管理",
                "查询构建器"
            });
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            logger.error("{} 获取ORM系统信息失败", OrmConstants.Log.CACHE_OPERATION_PREFIX, e);
            
            result.put("success", false);
            result.put("message", "获取ORM系统信息失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }
}
