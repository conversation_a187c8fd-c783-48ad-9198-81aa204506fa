package com.weaver.seconddev.zyhlw.ebuilder.repository;

import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.annotation.Repository;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepository;

/**
 * OutlayBudget Repository接口示例
 * 使用@Repository注解自动注册为Spring Bean
 * 继承BaseRepository获得标准CRUD操作
 * 可以在此基础上扩展自定义方法
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Repository(value = "outlayBudgetRepository", description = "预算支出Repository")
public interface OutlayBudgetRepository extends BaseRepository<OutlayBudgetDO> {

}
