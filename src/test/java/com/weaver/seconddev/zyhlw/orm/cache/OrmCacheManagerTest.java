package com.weaver.seconddev.zyhlw.orm.cache;

import com.weaver.seconddev.zyhlw.domain.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.factory.RepositoryFactory;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * OrmCacheManager单元测试
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class OrmCacheManagerTest {

    @Mock
    private EntityMetadataManager entityMetadataManager;

    @Mock
    private RepositoryFactory repositoryFactory;

    @InjectMocks
    private OrmCacheManager ormCacheManager;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testClearAllCache_Success() {
        // 执行测试
        ormCacheManager.clearAllCache();

        // 验证所有缓存清理方法都被调用
        verify(entityMetadataManager, times(1)).clearCache();
        verify(repositoryFactory, times(1)).clearCache();
    }

    @Test
    public void testClearAllCache_WithException() {
        // 准备测试数据 - 模拟异常
        doThrow(new RuntimeException("清理失败")).when(entityMetadataManager).clearCache();

        // 执行测试并验证异常
        try {
            ormCacheManager.clearAllCache();
            fail("应该抛出异常");
        } catch (RuntimeException e) {
            assertEquals("清空ORM缓存失败: 清理失败", e.getMessage());
        }

        // 验证方法被调用
        verify(entityMetadataManager, times(1)).clearCache();
    }

    @Test
    public void testClearEntityCache_Success() {
        // 执行测试
        ormCacheManager.clearEntityCache(OutlayBudgetDO.class);

        // 验证Repository缓存清理方法被调用
        verify(repositoryFactory, times(1)).removeFromCache(OutlayBudgetDO.class);
    }

    @Test
    public void testClearEntityCache_WithNullEntity() {
        // 执行测试
        ormCacheManager.clearEntityCache(null);

        // 验证Repository缓存清理方法没有被调用
        verify(repositoryFactory, never()).removeFromCache(any());
    }

    @Test
    public void testClearEntityCache_WithException() {
        // 准备测试数据 - 模拟异常
        doThrow(new RuntimeException("清理失败")).when(repositoryFactory).removeFromCache(OutlayBudgetDO.class);

        // 执行测试并验证异常
        try {
            ormCacheManager.clearEntityCache(OutlayBudgetDO.class);
            fail("应该抛出异常");
        } catch (RuntimeException e) {
            assertEquals("清空实体类缓存失败: 清理失败", e.getMessage());
        }

        // 验证方法被调用
        verify(repositoryFactory, times(1)).removeFromCache(OutlayBudgetDO.class);
    }

    @Test
    public void testGetAllCacheStats_Success() {
        // 准备测试数据
        when(entityMetadataManager.getCacheStats()).thenReturn("实体元数据缓存统计");
        when(repositoryFactory.getCacheStats()).thenReturn("Repository缓存统计");

        // 执行测试
        String stats = ormCacheManager.getAllCacheStats();

        // 验证结果
        assertNotNull("缓存统计不应为null", stats);
        assertTrue("应包含实体元数据缓存统计", stats.contains("实体元数据缓存统计"));
        assertTrue("应包含Repository缓存统计", stats.contains("Repository缓存统计"));
        assertTrue("应包含ORM缓存统计信息标题", stats.contains("=== ORM缓存统计信息 ==="));

        // 验证方法被调用
        verify(entityMetadataManager, times(1)).getCacheStats();
        verify(repositoryFactory, times(1)).getCacheStats();
    }

    @Test
    public void testGetAllCacheStats_WithException() {
        // 准备测试数据 - 模拟异常
        when(entityMetadataManager.getCacheStats()).thenThrow(new RuntimeException("获取统计失败"));

        // 执行测试
        String stats = ormCacheManager.getAllCacheStats();

        // 验证结果
        assertNotNull("缓存统计不应为null", stats);
        assertTrue("应包含错误信息", stats.contains("获取缓存统计信息失败"));
    }

    @Test
    public void testGetCacheHealthInfo_Success() {
        // 准备测试数据
        when(repositoryFactory.getCacheSize()).thenReturn(5);
        when(repositoryFactory.getCacheHitRate()).thenReturn(0.8);

        // 执行测试
        OrmCacheManager.CacheHealthInfo healthInfo = ormCacheManager.getCacheHealthInfo();

        // 验证结果
        assertNotNull("健康信息不应为null", healthInfo);
        assertTrue("应该是健康状态", healthInfo.isHealthy());
        assertEquals("Repository缓存大小应该正确", 5, healthInfo.getRepositoryCacheSize());
        assertEquals("Repository缓存命中率应该正确", 0.8, healthInfo.getRepositoryCacheHitRate(), 0.001);
        assertEquals("消息应该正确", "所有缓存运行正常", healthInfo.getMessage());

        // 验证方法被调用
        verify(repositoryFactory, times(1)).getCacheSize();
        verify(repositoryFactory, times(1)).getCacheHitRate();
    }

    @Test
    public void testGetCacheHealthInfo_WithException() {
        // 准备测试数据 - 模拟异常
        when(repositoryFactory.getCacheSize()).thenThrow(new RuntimeException("获取大小失败"));

        // 执行测试
        OrmCacheManager.CacheHealthInfo healthInfo = ormCacheManager.getCacheHealthInfo();

        // 验证结果
        assertNotNull("健康信息不应为null", healthInfo);
        assertFalse("应该是不健康状态", healthInfo.isHealthy());
        assertTrue("消息应包含错误信息", healthInfo.getMessage().contains("获取缓存健康状态失败"));
    }

    @Test
    public void testPrintAllCacheStats() {
        // 准备测试数据
        when(entityMetadataManager.getCacheStats()).thenReturn("实体元数据缓存统计");
        when(repositoryFactory.getCacheStats()).thenReturn("Repository缓存统计");

        // 执行测试（这个方法主要是打印日志，没有返回值）
        ormCacheManager.printAllCacheStats();

        // 验证方法被调用
        verify(entityMetadataManager, times(1)).getCacheStats();
        verify(repositoryFactory, times(1)).getCacheStats();
    }

    @Test
    public void testCacheHealthInfo_ToString() {
        // 创建健康信息对象
        OrmCacheManager.CacheHealthInfo healthInfo = new OrmCacheManager.CacheHealthInfo();
        healthInfo.setHealthy(true);
        healthInfo.setMessage("测试消息");
        healthInfo.setRepositoryCacheSize(10);
        healthInfo.setRepositoryCacheHitRate(0.75);
        healthInfo.setProxyCacheSize(3);

        // 执行测试
        String result = healthInfo.toString();

        // 验证结果
        assertNotNull("toString结果不应为null", result);
        assertTrue("应包含健康状态", result.contains("healthy=true"));
        assertTrue("应包含消息", result.contains("message='测试消息'"));
        assertTrue("应包含Repository缓存大小", result.contains("repositoryCacheSize=10"));
        assertTrue("应包含Repository缓存命中率", result.contains("repositoryCacheHitRate=75.00%"));
        assertTrue("应包含代理缓存大小", result.contains("proxyCacheSize=3"));
    }

    @Test
    public void testCacheHealthInfo_GettersAndSetters() {
        // 创建健康信息对象
        OrmCacheManager.CacheHealthInfo healthInfo = new OrmCacheManager.CacheHealthInfo();

        // 测试设置和获取
        healthInfo.setHealthy(true);
        assertTrue("健康状态应该正确", healthInfo.isHealthy());

        healthInfo.setMessage("测试消息");
        assertEquals("消息应该正确", "测试消息", healthInfo.getMessage());

        healthInfo.setRepositoryCacheSize(15);
        assertEquals("Repository缓存大小应该正确", 15, healthInfo.getRepositoryCacheSize());

        healthInfo.setRepositoryCacheHitRate(0.9);
        assertEquals("Repository缓存命中率应该正确", 0.9, healthInfo.getRepositoryCacheHitRate(), 0.001);

        healthInfo.setProxyCacheSize(7);
        assertEquals("代理缓存大小应该正确", 7, healthInfo.getProxyCacheSize());
    }
}
