package com.weaver.seconddev.zyhlw.orm.mapper;

import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import com.weaver.seconddev.zyhlw.orm.metadata.TableMetadata;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ResultMapperImpl单元测试
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class ResultMapperImplTest {

    @Mock
    private EntityMetadataManager entityMetadataManager;

    @Mock
    private TableMetadata tableMetadata;

    @InjectMocks
    private ResultMapperImpl resultMapper;

    private Map<String, Object> testResultMap;
    private OutlayBudgetDO testEntity;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // 准备测试数据
        testResultMap = new HashMap<>();
        testResultMap.put("id", 1L);
        testResultMap.put("budget_no", "BUD001");
        testResultMap.put("create_time", "2025-06-27 10:00:00");
        
        testEntity = new OutlayBudgetDO();
        testEntity.setId(1L);
        
        // 设置Mock行为
        when(entityMetadataManager.getTableMetadata(OutlayBudgetDO.class)).thenReturn(tableMetadata);
        
        // 模拟字段映射
        when(tableMetadata.getFieldName("id")).thenReturn("id");
        when(tableMetadata.getFieldName("budget_no")).thenReturn("budgetNo");
        when(tableMetadata.getFieldName("create_time")).thenReturn("createTime");
        
        when(tableMetadata.hasField("id")).thenReturn(true);
        when(tableMetadata.hasField("budgetNo")).thenReturn(true);
        when(tableMetadata.hasField("createTime")).thenReturn(true);
        
        // 模拟Field对象
        Field idField = OutlayBudgetDO.class.getDeclaredField("id");
        Field budgetNoField = OutlayBudgetDO.class.getDeclaredField("budgetNo");
        
        idField.setAccessible(true);
        budgetNoField.setAccessible(true);
        
        when(tableMetadata.getField("id")).thenReturn(idField);
        when(tableMetadata.getField("budgetNo")).thenReturn(budgetNoField);
        
        when(tableMetadata.getFieldType("id")).thenReturn(Long.class);
        when(tableMetadata.getFieldType("budgetNo")).thenReturn(String.class);
    }

    @Test
    public void testMapToEntity_Success() {
        // 执行测试
        OutlayBudgetDO result = resultMapper.mapToEntity(testResultMap, OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("ID应该正确映射", Long.valueOf(1L), result.getId());
        assertEquals("预算编号应该正确映射", "BUD001", result.getBudgetNo());
        
        // 验证Mock调用
        verify(entityMetadataManager, times(1)).getTableMetadata(OutlayBudgetDO.class);
    }

    @Test
    public void testMapToEntity_NullResultMap() {
        // 执行测试
        OutlayBudgetDO result = resultMapper.mapToEntity(null, OutlayBudgetDO.class);

        // 验证结果
        assertNull("结果应该为null", result);
    }

    @Test
    public void testMapToEntity_EmptyResultMap() {
        // 执行测试
        OutlayBudgetDO result = resultMapper.mapToEntity(new HashMap<>(), OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("结果不应为null", result);
        
        // 验证Mock调用
        verify(entityMetadataManager, times(1)).getTableMetadata(OutlayBudgetDO.class);
    }

    @Test(expected = OrmException.EntityMappingException.class)
    public void testMapToEntity_NullEntityClass() {
        // 执行测试，期望抛出异常
        resultMapper.mapToEntity(testResultMap, null);
    }

    @Test
    public void testMapToEntityList_Success() {
        // 准备测试数据
        List<Map<String, Object>> resultList = Arrays.asList(testResultMap, testResultMap);

        // 执行测试
        List<OutlayBudgetDO> result = resultMapper.mapToEntityList(resultList, OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果大小应该为2", 2, result.size());
        
        // 验证Mock调用
        verify(entityMetadataManager, times(2)).getTableMetadata(OutlayBudgetDO.class);
    }

    @Test
    public void testMapToEntityList_NullList() {
        // 执行测试
        List<OutlayBudgetDO> result = resultMapper.mapToEntityList(null, OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该为空列表", result.isEmpty());
    }

    @Test
    public void testMapToEntityList_EmptyList() {
        // 执行测试
        List<OutlayBudgetDO> result = resultMapper.mapToEntityList(Arrays.asList(), OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该为空列表", result.isEmpty());
    }

    @Test
    public void testMapToMap_Success() {
        // 准备测试数据
        Map<String, Field> fieldMap = new HashMap<>();
        Map<String, String> fieldToColumnMap = new HashMap<>();
        
        try {
            Field idField = OutlayBudgetDO.class.getDeclaredField("id");
            Field budgetNoField = OutlayBudgetDO.class.getDeclaredField("budgetNo");
            
            idField.setAccessible(true);
            budgetNoField.setAccessible(true);
            
            fieldMap.put("id", idField);
            fieldMap.put("budgetNo", budgetNoField);
            
            fieldToColumnMap.put("id", "id");
            fieldToColumnMap.put("budgetNo", "budget_no");
        } catch (NoSuchFieldException e) {
            fail("字段不存在: " + e.getMessage());
        }
        
        when(tableMetadata.getFieldMap()).thenReturn(fieldMap);
        when(tableMetadata.getColumnName("id")).thenReturn("id");
        when(tableMetadata.getColumnName("budgetNo")).thenReturn("budget_no");

        // 执行测试
        Map<String, Object> result = resultMapper.mapToMap(testEntity);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("ID应该正确映射", 1L, result.get("id"));
        
        // 验证Mock调用
        verify(entityMetadataManager, times(1)).getTableMetadata(OutlayBudgetDO.class);
    }

    @Test
    public void testMapToMap_NullEntity() {
        // 执行测试
        Map<String, Object> result = resultMapper.mapToMap(null);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该为空Map", result.isEmpty());
    }

    @Test
    public void testConvertValue_String() {
        // 执行测试
        String result = resultMapper.convertValue("test", String.class);

        // 验证结果
        assertEquals("字符串转换应该正确", "test", result);
    }

    @Test
    public void testConvertValue_Integer() {
        // 执行测试
        Integer result = resultMapper.convertValue("123", Integer.class);

        // 验证结果
        assertEquals("整数转换应该正确", Integer.valueOf(123), result);
    }

    @Test
    public void testConvertValue_Long() {
        // 执行测试
        Long result = resultMapper.convertValue("123", Long.class);

        // 验证结果
        assertEquals("长整数转换应该正确", Long.valueOf(123), result);
    }

    @Test
    public void testConvertValue_BigDecimal() {
        // 执行测试
        BigDecimal result = resultMapper.convertValue("123.45", BigDecimal.class);

        // 验证结果
        assertEquals("BigDecimal转换应该正确", new BigDecimal("123.45"), result);
    }

    @Test
    public void testConvertValue_Boolean() {
        // 执行测试
        Boolean result1 = resultMapper.convertValue("true", Boolean.class);
        Boolean result2 = resultMapper.convertValue(1, Boolean.class);
        Boolean result3 = resultMapper.convertValue(0, Boolean.class);

        // 验证结果
        assertTrue("布尔值转换应该正确", result1);
        assertTrue("数字1应该转换为true", result2);
        assertFalse("数字0应该转换为false", result3);
    }

    @Test
    public void testConvertValue_Null() {
        // 执行测试
        String result = resultMapper.convertValue(null, String.class);

        // 验证结果
        assertNull("null值转换应该返回null", result);
    }

    @Test
    public void testConvertFieldName() {
        // 设置Mock行为
        when(entityMetadataManager.underscoreToCamel("user_name")).thenReturn("userName");

        // 执行测试
        String result = resultMapper.convertFieldName("user_name");

        // 验证结果
        assertEquals("字段名转换应该正确", "userName", result);
        
        // 验证Mock调用
        verify(entityMetadataManager, times(1)).underscoreToCamel("user_name");
    }

    @Test
    public void testConvertToDbFieldName() {
        // 执行测试
        String result = resultMapper.convertToDbFieldName("userName");

        // 验证结果
        assertEquals("数据库字段名转换应该正确", "user_name", result);
    }

    @Test
    public void testIsEmpty() {
        // 执行测试
        assertTrue("null应该为空", resultMapper.isEmpty(null));
        assertTrue("空字符串应该为空", resultMapper.isEmpty(""));
        assertTrue("空白字符串应该为空", resultMapper.isEmpty("   "));
        assertFalse("非空字符串不应该为空", resultMapper.isEmpty("test"));
        assertTrue("空列表应该为空", resultMapper.isEmpty(Arrays.asList()));
        assertFalse("非空列表不应该为空", resultMapper.isEmpty(Arrays.asList("test")));
    }

    @Test
    public void testGetDefaultValue() {
        // 执行测试
        assertEquals("int默认值应该为0", Integer.valueOf(0), resultMapper.getDefaultValue(int.class));
        assertEquals("long默认值应该为0", Long.valueOf(0L), resultMapper.getDefaultValue(long.class));
        assertEquals("boolean默认值应该为false", Boolean.FALSE, resultMapper.getDefaultValue(boolean.class));
        assertNull("引用类型默认值应该为null", resultMapper.getDefaultValue(String.class));
    }
}
