package com.weaver.seconddev.zyhlw.orm.integration;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.executor.SqlExecutor;
import com.weaver.seconddev.zyhlw.orm.factory.RepositoryFactory;
import com.weaver.seconddev.zyhlw.orm.mapper.ResultMapper;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepository;
import com.weaver.seconddev.zyhlw.util.QueryWrapperSqlUtils;
import com.weaver.seconddev.zyhlw.util.page.PageInfo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ORM系统集成测试
 * 测试各个组件之间的协作和完整的数据流程
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class OrmIntegrationTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private SqlExecutor sqlExecutor;

    @Mock
    private ResultMapper resultMapper;

    private EntityMetadataManager entityMetadataManager;
    private RepositoryFactory repositoryFactory;
    private BaseRepository<OutlayBudgetDO> repository;

    private OutlayBudgetDO testEntity;
    private Map<String, Object> testResultMap;
    private QueryWrapperSqlUtils.CompleteSqlResult testSqlResult;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 创建真实的EntityMetadataManager实例
        entityMetadataManager = new EntityMetadataManager();
        
        // 创建RepositoryFactory实例
        repositoryFactory = new RepositoryFactory();
        repositoryFactory.setApplicationContext(applicationContext);
        
        // 准备测试数据
        testEntity = new OutlayBudgetDO();
        testEntity.setId(1L);
        testEntity.setBudgetNo("BUD001");
        
        testResultMap = new HashMap<>();
        testResultMap.put("id", 1L);
        testResultMap.put("yu_sd", "BUD001");
        testResultMap.put("create_time", "2025-06-27 10:00:00");
        
        testSqlResult = new QueryWrapperSqlUtils.CompleteSqlResult();
        testSqlResult.setSql("id = ?");
        testSqlResult.setTableName("outlay_budget");
        testSqlResult.setParams(Arrays.asList(1L));
        
        // 设置Mock行为
        when(applicationContext.getBean(any(Class.class))).thenAnswer(invocation -> {
            Class<?> clazz = invocation.getArgument(0);
            if (clazz.equals(com.weaver.seconddev.zyhlw.orm.repository.BaseRepositoryImpl.class)) {
                // 创建BaseRepositoryImpl实例并注入依赖
                com.weaver.seconddev.zyhlw.orm.repository.BaseRepositoryImpl<OutlayBudgetDO> impl = 
                    new com.weaver.seconddev.zyhlw.orm.repository.BaseRepositoryImpl<>();
                
                // 使用反射注入依赖
                try {
                    java.lang.reflect.Field sqlExecutorField = impl.getClass().getDeclaredField("sqlExecutor");
                    sqlExecutorField.setAccessible(true);
                    sqlExecutorField.set(impl, sqlExecutor);
                    
                    java.lang.reflect.Field resultMapperField = impl.getClass().getDeclaredField("resultMapper");
                    resultMapperField.setAccessible(true);
                    resultMapperField.set(impl, resultMapper);
                    
                    java.lang.reflect.Field entityMetadataManagerField = impl.getClass().getDeclaredField("entityMetadataManager");
                    entityMetadataManagerField.setAccessible(true);
                    entityMetadataManagerField.set(impl, entityMetadataManager);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to inject dependencies", e);
                }
                
                return impl;
            }
            return null;
        });
    }

    @Test
    public void testCompleteWorkflow_SelectList() {
        // 准备测试数据
        List<Map<String, Object>> resultList = Arrays.asList(testResultMap);
        List<OutlayBudgetDO> entityList = Arrays.asList(testEntity);
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeQuery(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(resultList);
        when(resultMapper.mapToEntityList(resultList, OutlayBudgetDO.class)).thenReturn(entityList);

        // 获取Repository实例
        repository = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 执行查询
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", 1L);
        List<OutlayBudgetDO> result = repository.selectList(queryWrapper);

        // 验证结果
        assertNotNull("查询结果不应为null", result);
        assertEquals("查询结果大小应该为1", 1, result.size());
        assertEquals("查询结果应该包含测试实体", testEntity, result.get(0));
        
        // 验证各组件的调用
        verify(sqlExecutor, times(1)).executeQuery(anyString(), anyList(), eq(SourceType.EXTERNAL));
        verify(resultMapper, times(1)).mapToEntityList(resultList, OutlayBudgetDO.class);
    }

    @Test
    public void testCompleteWorkflow_SelectById() {
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeQueryOne(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(testResultMap);
        when(resultMapper.mapToEntity(testResultMap, OutlayBudgetDO.class)).thenReturn(testEntity);

        // 获取Repository实例
        repository = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 执行查询
        OutlayBudgetDO result = repository.selectById(1L);

        // 验证结果
        assertNotNull("查询结果不应为null", result);
        assertEquals("查询结果应该是测试实体", testEntity, result);
        
        // 验证各组件的调用
        verify(sqlExecutor, times(1)).executeQueryOne(anyString(), anyList(), eq(SourceType.EXTERNAL));
        verify(resultMapper, times(1)).mapToEntity(testResultMap, OutlayBudgetDO.class);
    }

    @Test
    public void testCompleteWorkflow_Insert() {
        // 准备测试数据
        Map<String, Object> entityMap = new HashMap<>();
        entityMap.put("id", 1L);
        entityMap.put("yu_sd", "BUD001");
        
        // 设置Mock行为
        when(resultMapper.mapToMap(testEntity)).thenReturn(entityMap);
        when(sqlExecutor.executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(1);

        // 获取Repository实例
        repository = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 执行插入
        int result = repository.insert(testEntity);

        // 验证结果
        assertEquals("插入结果应该为1", 1, result);
        
        // 验证各组件的调用
        verify(resultMapper, times(1)).mapToMap(testEntity);
        verify(sqlExecutor, times(1)).executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL));
    }

    @Test
    public void testCompleteWorkflow_Update() {
        // 准备测试数据
        Map<String, Object> entityMap = new HashMap<>();
        entityMap.put("id", 1L);
        entityMap.put("yu_sd", "BUD001");
        
        // 设置Mock行为
        when(resultMapper.mapToMap(testEntity)).thenReturn(entityMap);
        when(sqlExecutor.executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(1);

        // 获取Repository实例
        repository = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 执行更新
        int result = repository.updateById(testEntity);

        // 验证结果
        assertEquals("更新结果应该为1", 1, result);
        
        // 验证各组件的调用
        verify(resultMapper, times(1)).mapToMap(testEntity);
        verify(sqlExecutor, times(1)).executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL));
    }

    @Test
    public void testCompleteWorkflow_Delete() {
        // 设置Mock行为
        when(sqlExecutor.executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(1);

        // 获取Repository实例
        repository = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 执行删除
        int result = repository.deleteById(1L);

        // 验证结果
        assertEquals("删除结果应该为1", 1, result);
        
        // 验证各组件的调用
        verify(sqlExecutor, times(1)).executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL));
    }

    @Test
    public void testCompleteWorkflow_Count() {
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeCount(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(5L);

        // 获取Repository实例
        repository = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 执行统计
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        long result = repository.selectCount(queryWrapper);

        // 验证结果
        assertEquals("统计结果应该为5", 5L, result);
        
        // 验证各组件的调用
        verify(sqlExecutor, times(1)).executeCount(anyString(), anyList(), eq(SourceType.EXTERNAL));
    }

    @Test
    public void testRepositoryFactory_Cache() {
        // 第一次获取Repository
        BaseRepository<OutlayBudgetDO> repository1 = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 第二次获取Repository（应该从缓存获取）
        BaseRepository<OutlayBudgetDO> repository2 = repositoryFactory.getRepository(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("第一次获取的Repository不应为null", repository1);
        assertNotNull("第二次获取的Repository不应为null", repository2);
        assertSame("两次获取的Repository应该是同一个实例", repository1, repository2);
        
        // 验证只调用了一次Spring容器
        verify(applicationContext, times(1)).getBean(any(Class.class));
    }

    @Test
    public void testEntityMetadataManager_RealData() {
        // 测试真实的元数据管理器
        var metadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("元数据不应为null", metadata);
        assertEquals("实体类应该正确", OutlayBudgetDO.class, metadata.getEntityClass());
        assertNotNull("表名不应为null", metadata.getTableName());
        assertTrue("应该包含id字段", metadata.hasField("id"));
        assertEquals("主键字段应该为id", "id", metadata.getPrimaryKeyField());
    }

    @Test
    public void testTypeReference_Integration() {
        // 使用TypeReference获取Repository
        RepositoryFactory.TypeReference<OutlayBudgetDO> typeReference = 
            new RepositoryFactory.TypeReference<OutlayBudgetDO>() {};
        
        BaseRepository<OutlayBudgetDO> repository = repositoryFactory.getRepository(typeReference);

        // 验证结果
        assertNotNull("Repository不应为null", repository);
        
        // 验证调用了Spring容器
        verify(applicationContext, times(1)).getBean(any(Class.class));
    }
}
