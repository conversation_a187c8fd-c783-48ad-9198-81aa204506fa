package com.weaver.seconddev.zyhlw.orm.factory;

import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepository;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepositoryImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * RepositoryFactory单元测试
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class RepositoryFactoryTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private BaseRepositoryImpl<OutlayBudgetDO> baseRepositoryImpl;

    @InjectMocks
    private RepositoryFactory repositoryFactory;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 清空缓存
        repositoryFactory.clearCache();
    }

    @Test
    public void testGetRepository_Success() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 执行测试
        BaseRepository<OutlayBudgetDO> repository = repositoryFactory.getRepository(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("Repository实例不应为null", repository);
        verify(applicationContext, times(1)).getBean(BaseRepositoryImpl.class);
        verify(baseRepositoryImpl, times(1)).setEntityClass(OutlayBudgetDO.class);
    }

    @Test
    public void testGetRepository_Cache() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 第一次调用
        BaseRepository<OutlayBudgetDO> repository1 = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 第二次调用（应该从缓存获取）
        BaseRepository<OutlayBudgetDO> repository2 = repositoryFactory.getRepository(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("第一次获取的Repository不应为null", repository1);
        assertNotNull("第二次获取的Repository不应为null", repository2);
        assertSame("两次获取的Repository应该是同一个实例", repository1, repository2);
        
        // 验证只调用了一次Spring容器
        verify(applicationContext, times(1)).getBean(BaseRepositoryImpl.class);
        verify(baseRepositoryImpl, times(1)).setEntityClass(OutlayBudgetDO.class);
    }

    @Test
    public void testGetRepository_NullEntityClass() {
        // 执行测试，期望抛出异常
        assertThrows(OrmException.ConfigurationException.class, () -> {
            repositoryFactory.getRepository((Class<OutlayBudgetDO>) null);
        });
    }

    @Test
    public void testGetRepository_WithTypeReference() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 创建类型引用
        RepositoryFactory.TypeReference<OutlayBudgetDO> typeReference = 
            new RepositoryFactory.TypeReference<OutlayBudgetDO>() {};

        // 执行测试
        BaseRepository<OutlayBudgetDO> repository = repositoryFactory.getRepository(typeReference);

        // 验证结果
        assertNotNull("Repository实例不应为null", repository);
        verify(applicationContext, times(1)).getBean(BaseRepositoryImpl.class);
        verify(baseRepositoryImpl, times(1)).setEntityClass(OutlayBudgetDO.class);
    }

    @Test
    public void testGetRepository_NullTypeReference() {
        // 执行测试，期望抛出异常
        assertThrows(OrmException.ConfigurationException.class, () -> {
            repositoryFactory.getRepository((RepositoryFactory.TypeReference<OutlayBudgetDO>) null);
        });
    }

    @Test
    public void testRemoveFromCache() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 先获取Repository（会缓存）
        repositoryFactory.getRepository(OutlayBudgetDO.class);
        assertTrue("缓存应该包含该实体类", repositoryFactory.isCached(OutlayBudgetDO.class));

        // 移除缓存
        repositoryFactory.removeFromCache(OutlayBudgetDO.class);
        assertFalse("缓存应该不再包含该实体类", repositoryFactory.isCached(OutlayBudgetDO.class));
    }

    @Test
    public void testClearCache() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 获取Repository（会缓存）
        repositoryFactory.getRepository(OutlayBudgetDO.class);
        assertTrue("缓存大小应该大于0", repositoryFactory.getCacheSize() > 0);

        // 清空缓存
        repositoryFactory.clearCache();
        assertEquals("缓存大小应该为0", 0, repositoryFactory.getCacheSize());
    }

    @Test
    public void testGetCacheStats() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 执行一些操作来产生缓存统计
        repositoryFactory.getRepository(OutlayBudgetDO.class); // 缓存未命中
        repositoryFactory.getRepository(OutlayBudgetDO.class); // 缓存命中

        // 获取缓存统计
        String stats = repositoryFactory.getCacheStats();
        assertNotNull("缓存统计不应为null", stats);
        assertTrue("缓存统计应包含相关信息", stats.contains("Repository缓存统计"));
        assertTrue("缓存统计应包含命中率信息", stats.contains("命中率"));
    }

    @Test
    public void testGetCacheHitRate() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 初始命中率应该为0
        assertEquals("初始命中率应该为0", 0.0, repositoryFactory.getCacheHitRate(), 0.001);

        // 执行操作
        repositoryFactory.getRepository(OutlayBudgetDO.class); // 缓存未命中
        repositoryFactory.getRepository(OutlayBudgetDO.class); // 缓存命中

        // 验证命中率
        double hitRate = repositoryFactory.getCacheHitRate();
        assertTrue("命中率应该大于0", hitRate > 0);
        assertTrue("命中率应该小于等于1", hitRate <= 1.0);
    }

    @Test
    public void testIsCached() {
        // 准备测试数据
        when(applicationContext.getBean(BaseRepositoryImpl.class)).thenReturn(baseRepositoryImpl);

        // 初始状态应该未缓存
        assertFalse("初始状态应该未缓存", repositoryFactory.isCached(OutlayBudgetDO.class));

        // 获取Repository后应该被缓存
        repositoryFactory.getRepository(OutlayBudgetDO.class);
        assertTrue("获取Repository后应该被缓存", repositoryFactory.isCached(OutlayBudgetDO.class));
    }

    @Test
    public void testTypeReference_InvalidType() {
        try {
            // 创建无效的类型引用（没有泛型参数）
            @SuppressWarnings("rawtypes")
            RepositoryFactory.TypeReference invalidTypeReference = new RepositoryFactory.TypeReference() {};
            
            fail("应该抛出ConfigurationException");
        } catch (OrmException.ConfigurationException e) {
            // 期望的异常
            assertTrue("异常消息应该包含相关信息", e.getMessage().contains("TypeReference必须使用泛型参数"));
        }
    }
}
