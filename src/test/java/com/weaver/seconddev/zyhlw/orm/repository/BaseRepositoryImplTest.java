package com.weaver.seconddev.zyhlw.orm.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.executor.SqlExecutor;
import com.weaver.seconddev.zyhlw.orm.mapper.ResultMapper;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import com.weaver.seconddev.zyhlw.orm.metadata.TableMetadata;
import com.weaver.seconddev.zyhlw.util.QueryWrapperSqlUtils;
import com.weaver.seconddev.zyhlw.util.page.PageInfo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BaseRepositoryImpl单元测试
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class BaseRepositoryImplTest {

    @Mock
    private SqlExecutor sqlExecutor;

    @Mock
    private ResultMapper resultMapper;

    @Mock
    private EntityMetadataManager entityMetadataManager;

    @Mock
    private TableMetadata tableMetadata;

    @InjectMocks
    private BaseRepositoryImpl<OutlayBudgetDO> repository;

    private OutlayBudgetDO testEntity;
    private Map<String, Object> testResultMap;
    private QueryWrapperSqlUtils.CompleteSqlResult testSqlResult;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 设置实体类
        repository.setEntityClass(OutlayBudgetDO.class);
        
        // 准备测试数据
        testEntity = new OutlayBudgetDO();
        testEntity.setId(1L);
        
        testResultMap = new HashMap<>();
        testResultMap.put("id", 1L);
        testResultMap.put("budget_no", "BUD001");
        
        testSqlResult = new QueryWrapperSqlUtils.CompleteSqlResult();
        testSqlResult.setSql("id = ?");
        testSqlResult.setTableName("outlay_budget");
        testSqlResult.setParams(Arrays.asList(1L));
        
        // 设置Mock行为
        when(entityMetadataManager.getTableMetadata(OutlayBudgetDO.class)).thenReturn(tableMetadata);
        when(tableMetadata.getTableName()).thenReturn("outlay_budget");
        when(tableMetadata.getPrimaryKeyField()).thenReturn("id");
        when(tableMetadata.getPrimaryKeyColumn()).thenReturn("id");
    }

    @Test
    public void testSelectList_Success() {
        // 准备测试数据
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        List<Map<String, Object>> resultList = Arrays.asList(testResultMap);
        List<OutlayBudgetDO> entityList = Arrays.asList(testEntity);
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeQuery(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(resultList);
        when(resultMapper.mapToEntityList(resultList, OutlayBudgetDO.class)).thenReturn(entityList);

        // 执行测试
        List<OutlayBudgetDO> result = repository.selectList(queryWrapper);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果大小应该为1", 1, result.size());
        assertEquals("结果应该包含测试实体", testEntity, result.get(0));
        
        // 验证Mock调用
        verify(sqlExecutor, times(1)).executeQuery(anyString(), anyList(), eq(SourceType.EXTERNAL));
        verify(resultMapper, times(1)).mapToEntityList(resultList, OutlayBudgetDO.class);
    }

    @Test
    public void testSelectOne_Success() {
        // 准备测试数据
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeQueryOne(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(testResultMap);
        when(resultMapper.mapToEntity(testResultMap, OutlayBudgetDO.class)).thenReturn(testEntity);

        // 执行测试
        OutlayBudgetDO result = repository.selectOne(queryWrapper);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果应该是测试实体", testEntity, result);
        
        // 验证Mock调用
        verify(sqlExecutor, times(1)).executeQueryOne(anyString(), anyList(), eq(SourceType.EXTERNAL));
        verify(resultMapper, times(1)).mapToEntity(testResultMap, OutlayBudgetDO.class);
    }

    @Test
    public void testSelectById_Success() {
        // 准备测试数据
        Long id = 1L;
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeQueryOne(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(testResultMap);
        when(resultMapper.mapToEntity(testResultMap, OutlayBudgetDO.class)).thenReturn(testEntity);

        // 执行测试
        OutlayBudgetDO result = repository.selectById(id);

        // 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果应该是测试实体", testEntity, result);
    }

    @Test
    public void testSelectById_NullId() {
        // 执行测试
        OutlayBudgetDO result = repository.selectById(null);

        // 验证结果
        assertNull("结果应该为null", result);
        
        // 验证没有调用SQL执行器
        verify(sqlExecutor, never()).executeQueryOne(anyString(), anyList(), any());
    }

    @Test
    public void testSelectCount_Success() {
        // 准备测试数据
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        long expectedCount = 5L;
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeCount(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(expectedCount);

        // 执行测试
        long result = repository.selectCount(queryWrapper);

        // 验证结果
        assertEquals("统计结果应该正确", expectedCount, result);
        
        // 验证Mock调用
        verify(sqlExecutor, times(1)).executeCount(anyString(), anyList(), eq(SourceType.EXTERNAL));
    }

    @Test
    public void testInsert_Success() {
        // 准备测试数据
        Map<String, Object> entityMap = new HashMap<>();
        entityMap.put("id", 1L);
        entityMap.put("budget_no", "BUD001");
        int expectedResult = 1;
        
        // 设置Mock行为
        when(resultMapper.mapToMap(testEntity)).thenReturn(entityMap);
        when(sqlExecutor.executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(expectedResult);

        // 执行测试
        int result = repository.insert(testEntity);

        // 验证结果
        assertEquals("插入结果应该正确", expectedResult, result);
        
        // 验证Mock调用
        verify(resultMapper, times(1)).mapToMap(testEntity);
        verify(sqlExecutor, times(1)).executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL));
    }

    @Test(expected = OrmException.SqlExecutionException.class)
    public void testInsert_NullEntity() {
        // 执行测试，期望抛出异常
        repository.insert(null);
    }

    @Test
    public void testDeleteById_Success() {
        // 准备测试数据
        Long id = 1L;
        int expectedResult = 1;
        
        // 设置Mock行为
        when(sqlExecutor.executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(expectedResult);

        // 执行测试
        int result = repository.deleteById(id);

        // 验证结果
        assertEquals("删除结果应该正确", expectedResult, result);
        
        // 验证Mock调用
        verify(sqlExecutor, times(1)).executeUpdate(anyString(), anyList(), eq(SourceType.EXTERNAL));
    }

    @Test
    public void testDeleteById_NullId() {
        // 执行测试
        int result = repository.deleteById(null);

        // 验证结果
        assertEquals("删除结果应该为0", 0, result);
        
        // 验证没有调用SQL执行器
        verify(sqlExecutor, never()).executeUpdate(anyString(), anyList(), any());
    }

    @Test
    public void testExists_True() {
        // 准备测试数据
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeCount(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(1L);

        // 执行测试
        boolean result = repository.exists(queryWrapper);

        // 验证结果
        assertTrue("存在性检查应该返回true", result);
    }

    @Test
    public void testExists_False() {
        // 准备测试数据
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeCount(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(0L);

        // 执行测试
        boolean result = repository.exists(queryWrapper);

        // 验证结果
        assertFalse("存在性检查应该返回false", result);
    }

    @Test
    public void testExistsById_True() {
        // 准备测试数据
        Long id = 1L;
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeQueryOne(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(testResultMap);
        when(resultMapper.mapToEntity(testResultMap, OutlayBudgetDO.class)).thenReturn(testEntity);

        // 执行测试
        boolean result = repository.existsById(id);

        // 验证结果
        assertTrue("ID存在性检查应该返回true", result);
    }

    @Test
    public void testExistsById_False() {
        // 准备测试数据
        Long id = 1L;
        
        // 设置Mock行为
        mockStatic(QueryWrapperSqlUtils.class);
        when(QueryWrapperSqlUtils.extractCompleteSql(any())).thenReturn(testSqlResult);
        when(sqlExecutor.executeQueryOne(anyString(), anyList(), eq(SourceType.EXTERNAL))).thenReturn(null);
        when(resultMapper.mapToEntity(null, OutlayBudgetDO.class)).thenReturn(null);

        // 执行测试
        boolean result = repository.existsById(id);

        // 验证结果
        assertFalse("ID存在性检查应该返回false", result);
    }
}
