package com.weaver.seconddev.zyhlw.orm;

import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.factory.RepositoryFactory;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import com.weaver.seconddev.zyhlw.orm.metadata.TableMetadata;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ORM系统核心功能测试
 * 测试主要组件的基本功能
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class OrmSystemTest {

    @Mock
    private ApplicationContext applicationContext;

    private EntityMetadataManager entityMetadataManager;
    private RepositoryFactory repositoryFactory;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建真实的EntityMetadataManager实例
        entityMetadataManager = new EntityMetadataManager();
        
        // 创建RepositoryFactory实例
        repositoryFactory = new RepositoryFactory();
        // 使用反射设置ApplicationContext
        try {
            java.lang.reflect.Field field = RepositoryFactory.class.getDeclaredField("applicationContext");
            field.setAccessible(true);
            field.set(repositoryFactory, applicationContext);
        } catch (Exception e) {
            // 忽略反射异常
        }
    }

    @Test
    public void testEntityMetadataManager_BasicFunctionality() {
        // 测试获取表元数据
        TableMetadata metadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证基本信息
        assertNotNull(metadata, "元数据不应为null");
        assertEquals(OutlayBudgetDO.class, metadata.getEntityClass(), "实体类应该正确");
        assertNotNull(metadata.getTableName(), "表名不应为null");
        assertFalse(metadata.getTableName().trim().isEmpty(), "表名不应为空");
        
        // 验证主键信息
        assertNotNull(metadata.getPrimaryKeyField(), "主键字段不应为null");
        assertNotNull(metadata.getPrimaryKeyColumn(), "主键列名不应为null");
        assertEquals("id", metadata.getPrimaryKeyField(), "主键字段应该为id");
        
        // 验证字段映射
        assertTrue(metadata.hasField("id"), "应该包含id字段");
        assertNotNull(metadata.getFieldType("id"), "id字段类型不应为null");
        assertNotNull(metadata.getField("id"), "id字段的Field对象不应为null");
    }

    @Test
    public void testEntityMetadataManager_Cache() {
        // 第一次调用
        TableMetadata metadata1 = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);
        
        // 第二次调用（应该从缓存获取）
        TableMetadata metadata2 = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证缓存工作正常
        assertNotNull(metadata1, "第一次获取的元数据不应为null");
        assertNotNull(metadata2, "第二次获取的元数据不应为null");
        assertSame(metadata1, metadata2, "两次获取的元数据应该是同一个实例");
    }

    @Test
    public void testEntityMetadataManager_FieldMapping() {
        TableMetadata metadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 测试字段映射功能
        if (metadata.hasField("budgetNo")) {
            String columnName = metadata.getColumnName("budgetNo");
            assertNotNull(columnName, "budgetNo字段的列名不应为null");
            
            // 验证反向映射
            String fieldName = metadata.getFieldName(columnName);
            assertEquals("budgetNo", fieldName, "反向映射应该正确");
        }
    }

    @Test
    public void testEntityMetadataManager_UnderscoreToCamel() {
        // 测试下划线转驼峰功能
        String result1 = entityMetadataManager.underscoreToCamel("user_name");
        String result2 = entityMetadataManager.underscoreToCamel("create_time");
        String result3 = entityMetadataManager.underscoreToCamel("id");

        assertEquals("userName", result1, "user_name应该转换为userName");
        assertEquals("createTime", result2, "create_time应该转换为createTime");
        assertEquals("id", result3, "id应该保持不变");
    }

    @Test
    public void testEntityMetadataManager_NullHandling() {
        // 测试null值处理
        assertThrows(Exception.class, () -> {
            entityMetadataManager.getTableMetadata(null);
        }, "null实体类应该抛出异常");
        
        // 测试下划线转驼峰的null处理
        String result = entityMetadataManager.underscoreToCamel(null);
        assertNull(result, "null输入应该返回null");
    }

    @Test
    public void testRepositoryFactory_TypeReference() {
        // 测试TypeReference功能
        RepositoryFactory.TypeReference<OutlayBudgetDO> typeReference = 
            new RepositoryFactory.TypeReference<OutlayBudgetDO>() {};
        
        assertNotNull(typeReference.getType(), "类型引用不应为null");
        assertEquals(OutlayBudgetDO.class, typeReference.getType(), "类型引用应该正确");
    }

    @Test
    public void testRepositoryFactory_InvalidTypeReference() {
        // 测试无效的TypeReference
        assertThrows(Exception.class, () -> {
            @SuppressWarnings("rawtypes")
            RepositoryFactory.TypeReference invalidTypeReference = new RepositoryFactory.TypeReference() {};
        }, "无效的TypeReference应该抛出异常");
    }

    @Test
    public void testRepositoryFactory_CacheOperations() {
        // 测试缓存操作
        assertEquals(0, repositoryFactory.getCacheSize(), "初始缓存大小应该为0");
        assertFalse(repositoryFactory.isCached(OutlayBudgetDO.class), "初始状态应该未缓存");
        
        // 清空缓存
        repositoryFactory.clearCache();
        assertEquals(0, repositoryFactory.getCacheSize(), "清空后缓存大小应该为0");
        
        // 获取缓存统计
        String stats = repositoryFactory.getCacheStats();
        assertNotNull(stats, "缓存统计不应为null");
        assertTrue(stats.contains("Repository缓存统计"), "缓存统计应包含相关信息");
    }

    @Test
    public void testRepositoryFactory_HitRate() {
        // 测试缓存命中率
        double initialHitRate = repositoryFactory.getCacheHitRate();
        assertEquals(0.0, initialHitRate, 0.001, "初始命中率应该为0");
        
        // 清空缓存重置统计
        repositoryFactory.clearCache();
        double resetHitRate = repositoryFactory.getCacheHitRate();
        assertEquals(0.0, resetHitRate, 0.001, "重置后命中率应该为0");
    }

    @Test
    public void testTableMetadata_BasicOperations() {
        // 创建TableMetadata实例进行测试
        TableMetadata metadata = new TableMetadata(OutlayBudgetDO.class, "test_table");
        
        // 测试基本属性
        assertEquals(OutlayBudgetDO.class, metadata.getEntityClass(), "实体类应该正确");
        assertEquals("test_table", metadata.getTableName(), "表名应该正确");
        
        // 测试字段映射
        try {
            java.lang.reflect.Field budgetNoField = OutlayBudgetDO.class.getDeclaredField("budgetNo");
            metadata.addFieldMapping("budgetNo", "yu_sd", String.class, budgetNoField);

            assertTrue(metadata.hasField("budgetNo"), "应该包含budgetNo字段");
            assertTrue(metadata.hasColumn("yu_sd"), "应该包含yu_sd列");
            assertEquals("yu_sd", metadata.getColumnName("budgetNo"), "列名映射应该正确");
            assertEquals("budgetNo", metadata.getFieldName("yu_sd"), "字段名映射应该正确");
            assertEquals(String.class, metadata.getFieldType("budgetNo"), "字段类型应该正确");
            assertEquals(budgetNoField, metadata.getField("budgetNo"), "Field对象应该正确");
        } catch (NoSuchFieldException e) {
            fail("字段不存在: " + e.getMessage());
        }
    }

    @Test
    public void testTableMetadata_SpecialFields() {
        TableMetadata metadata = new TableMetadata(OutlayBudgetDO.class, "test_table");
        
        // 测试主键设置
        metadata.setPrimaryKeyField("id");
        metadata.setPrimaryKeyColumn("id");
        assertEquals("id", metadata.getPrimaryKeyField(), "主键字段应该正确");
        assertEquals("id", metadata.getPrimaryKeyColumn(), "主键列名应该正确");
        
        // 测试逻辑删除设置
        metadata.setHasLogicDelete(true);
        metadata.setLogicDeleteField("deleteType");
        metadata.setLogicDeleteColumn("delete_type");
        assertTrue(metadata.isHasLogicDelete(), "应该有逻辑删除字段");
        assertEquals("deleteType", metadata.getLogicDeleteField(), "逻辑删除字段应该正确");
        assertEquals("delete_type", metadata.getLogicDeleteColumn(), "逻辑删除列名应该正确");
        
        // 测试租户设置
        metadata.setHasTenant(true);
        metadata.setTenantField("tenantKey");
        metadata.setTenantColumn("tenant_key");
        assertTrue(metadata.isHasTenant(), "应该有租户字段");
        assertEquals("tenantKey", metadata.getTenantField(), "租户字段应该正确");
        assertEquals("tenant_key", metadata.getTenantColumn(), "租户列名应该正确");
    }

    @Test
    public void testEntityMetadataManager_CacheStats() {
        // 执行一些操作来产生缓存统计
        entityMetadataManager.getTableMetadata(OutlayBudgetDO.class); // 缓存未命中
        entityMetadataManager.getTableMetadata(OutlayBudgetDO.class); // 缓存命中

        // 获取缓存统计
        String stats = entityMetadataManager.getCacheStats();
        assertNotNull(stats, "缓存统计不应为null");
        assertTrue(stats.contains("缓存统计"), "缓存统计应包含相关信息");
        
        // 清空缓存
        entityMetadataManager.clearCache();
        
        // 验证缓存已清空
        String statsAfterClear = entityMetadataManager.getCacheStats();
        assertNotNull(statsAfterClear, "清空后的缓存统计不应为null");
    }
}
