package com.weaver.seconddev.zyhlw.orm.metadata;

import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * EntityMetadataManager单元测试
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class EntityMetadataManagerTest {

    private EntityMetadataManager entityMetadataManager;

    @Before
    public void setUp() {
        entityMetadataManager = new EntityMetadataManager();
        // 清空缓存
        entityMetadataManager.clearCache();
    }

    @Test
    public void testGetTableMetadata_Success() {
        // 执行测试
        TableMetadata metadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("元数据不应为null", metadata);
        assertEquals("实体类应该正确", OutlayBudgetDO.class, metadata.getEntityClass());
        assertNotNull("表名不应为null", metadata.getTableName());
        assertNotNull("主键字段不应为null", metadata.getPrimaryKeyField());
        assertNotNull("主键列名不应为null", metadata.getPrimaryKeyColumn());
    }

    @Test(expected = OrmException.MetadataException.class)
    public void testGetTableMetadata_NullEntityClass() {
        // 执行测试，期望抛出异常
        entityMetadataManager.getTableMetadata(null);
    }

    @Test
    public void testGetTableMetadata_Cache() {
        // 第一次调用
        TableMetadata metadata1 = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);
        
        // 第二次调用（应该从缓存获取）
        TableMetadata metadata2 = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("第一次获取的元数据不应为null", metadata1);
        assertNotNull("第二次获取的元数据不应为null", metadata2);
        assertSame("两次获取的元数据应该是同一个实例", metadata1, metadata2);
    }

    @Test
    public void testGetTableName() {
        // 执行测试
        String tableName = entityMetadataManager.getTableName(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("表名不应为null", tableName);
        assertFalse("表名不应为空", tableName.trim().isEmpty());
    }

    @Test
    public void testGetPrimaryKeyField() {
        // 执行测试
        String primaryKeyField = entityMetadataManager.getPrimaryKeyField(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("主键字段不应为null", primaryKeyField);
        assertEquals("主键字段应该为id", "id", primaryKeyField);
    }

    @Test
    public void testGetPrimaryKeyColumn() {
        // 执行测试
        String primaryKeyColumn = entityMetadataManager.getPrimaryKeyColumn(OutlayBudgetDO.class);

        // 验证结果
        assertNotNull("主键列名不应为null", primaryKeyColumn);
        assertEquals("主键列名应该为id", "id", primaryKeyColumn);
    }

    @Test
    public void testUnderscoreToCamel() {
        // 执行测试
        String result1 = entityMetadataManager.underscoreToCamel("user_name");
        String result2 = entityMetadataManager.underscoreToCamel("create_time");
        String result3 = entityMetadataManager.underscoreToCamel("id");

        // 验证结果
        assertEquals("下划线转驼峰应该正确", "userName", result1);
        assertEquals("下划线转驼峰应该正确", "createTime", result2);
        assertEquals("单个单词应该保持不变", "id", result3);
    }

    @Test
    public void testUnderscoreToCamel_NullOrEmpty() {
        // 执行测试
        String result1 = entityMetadataManager.underscoreToCamel(null);
        String result2 = entityMetadataManager.underscoreToCamel("");

        // 验证结果
        assertNull("null输入应该返回null", result1);
        assertEquals("空字符串输入应该返回空字符串", "", result2);
    }

    @Test
    public void testClearCache() {
        // 先获取元数据（会缓存）
        entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);
        
        // 清空缓存
        entityMetadataManager.clearCache();
        
        // 验证缓存已清空（通过获取缓存统计信息）
        String stats = entityMetadataManager.getCacheStats();
        assertNotNull("缓存统计不应为null", stats);
        assertTrue("缓存统计应包含相关信息", stats.contains("缓存统计"));
    }

    @Test
    public void testGetCacheStats() {
        // 执行一些操作来产生缓存统计
        entityMetadataManager.getTableMetadata(OutlayBudgetDO.class); // 缓存未命中
        entityMetadataManager.getTableMetadata(OutlayBudgetDO.class); // 缓存命中

        // 获取缓存统计
        String stats = entityMetadataManager.getCacheStats();
        
        // 验证结果
        assertNotNull("缓存统计不应为null", stats);
        assertTrue("缓存统计应包含相关信息", stats.contains("缓存统计"));
        assertTrue("缓存统计应包含命中信息", stats.contains("命中"));
    }

    @Test
    public void testTableMetadata_FieldMapping() {
        // 获取元数据
        TableMetadata metadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证字段映射
        assertTrue("应该包含id字段", metadata.hasField("id"));
        assertNotNull("id字段的列名不应为null", metadata.getColumnName("id"));
        assertNotNull("id字段的类型不应为null", metadata.getFieldType("id"));
        assertNotNull("id字段的Field对象不应为null", metadata.getField("id"));
    }

    @Test
    public void testTableMetadata_LogicDelete() {
        // 获取元数据
        TableMetadata metadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证逻辑删除字段
        if (metadata.isHasLogicDelete()) {
            assertNotNull("逻辑删除字段不应为null", metadata.getLogicDeleteField());
            assertNotNull("逻辑删除列名不应为null", metadata.getLogicDeleteColumn());
            assertEquals("逻辑删除字段应该为deleteType", "deleteType", metadata.getLogicDeleteField());
        }
    }

    @Test
    public void testTableMetadata_Tenant() {
        // 获取元数据
        TableMetadata metadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);

        // 验证租户字段
        if (metadata.isHasTenant()) {
            assertNotNull("租户字段不应为null", metadata.getTenantField());
            assertNotNull("租户列名不应为null", metadata.getTenantColumn());
            assertEquals("租户字段应该为tenantKey", "tenantKey", metadata.getTenantField());
        }
    }

    @Test
    public void testGetTableMetadataByTableName() {
        // 先获取元数据（会缓存）
        TableMetadata originalMetadata = entityMetadataManager.getTableMetadata(OutlayBudgetDO.class);
        String tableName = originalMetadata.getTableName();
        
        // 通过表名获取元数据
        TableMetadata metadata = entityMetadataManager.getTableMetadataByTableName(tableName);

        // 验证结果
        if (metadata != null) {
            assertEquals("通过表名获取的元数据应该相同", originalMetadata, metadata);
        }
    }

    @Test
    public void testGetTableMetadataByTableName_NotFound() {
        // 执行测试
        TableMetadata metadata = entityMetadataManager.getTableMetadataByTableName("non_existent_table");

        // 验证结果
        assertNull("不存在的表名应该返回null", metadata);
    }

    /**
     * 测试实体类，用于验证注解解析
     */
    public static class TestEntity {
        private Long id;
        private String name;
        
        // getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    @Test
    public void testGetTableMetadata_WithoutAnnotation() {
        // 执行测试
        TableMetadata metadata = entityMetadataManager.getTableMetadata(TestEntity.class);

        // 验证结果
        assertNotNull("元数据不应为null", metadata);
        assertEquals("实体类应该正确", TestEntity.class, metadata.getEntityClass());
        assertNotNull("表名不应为null", metadata.getTableName());
        // 没有@TableName注解时，应该使用类名转换
        assertEquals("表名应该为类名转换结果", "test_entity", metadata.getTableName());
    }
}
